use duckdb::core::{DataChunkHandle, LogicalTypeId};
use duckdb::vtab::{BindInfo, InitInfo, VTab};
use duckdb::{ffi, Connection};
use duckdb_loadable_macros::duckdb_entrypoint_c_api;

use crate::types::{InferredJsonType, SchemaInferenceConfig};
use crate::schema::{InferredSchema, infer_schema_with_capacities};

#[repr(C)]
pub struct JsonReaderBindData {
    file_path: String,
    schema: InferredSchema,
}

#[repr(C)]
pub struct JsonReaderInitData {
    finished: std::sync::atomic::AtomicBool,
}

pub struct JsonReaderVTab;

impl VTab for JsonReaderVTab {
    type InitData = JsonReaderInitData;
    type BindData = JsonReaderBindData;

    fn bind(bind: &BindInfo) -> duckdb::Result<Self::BindData, Box<dyn std::error::Error>> {
        // Check if we have parameters
        if bind.get_parameter_count() == 0 {
            return Err("streaming_json_reader requires a file path parameter".into());
        }

        // Get file path from first parameter
        let file_path = bind.get_parameter(0).to_string();

        // Perform schema inference with capacity calculation (first pass)
        let config = SchemaInferenceConfig {
            enable_debug_output: false,
        };

        let schema = infer_schema_with_capacities(&file_path, config)
            .map_err(|e| format!("Schema inference failed: {}", e))?;

        // Add columns based on inferred schema
        match &schema.root_type {
            InferredJsonType::Array { element_type, .. } => {
                match element_type.as_ref() {
                    InferredJsonType::Object { fields, .. } => {
                        // Array of objects - each field becomes a column
                        for (field_name, field_type) in fields {
                            let logical_type = create_duckdb_type(field_type)?;
                            bind.add_result_column(field_name, logical_type);
                        }
                    },
                    _ => {
                        // Array of non-objects - single column
                        let logical_type = create_duckdb_type(element_type)?;
                        bind.add_result_column("column0", logical_type);
                    }
                }
            },
            InferredJsonType::Object { fields, .. } => {
                // Single object - each field becomes a column
                if fields.is_empty() {
                    // Empty object - add a fallback column to satisfy DuckDB's requirement
                    // that table functions must return at least one column
                    let logical_type = duckdb::core::LogicalTypeHandle::from(LogicalTypeId::Varchar);
                    bind.add_result_column("json", logical_type);
                } else {
                    for (field_name, field_type) in fields {
                        let logical_type = create_duckdb_type(field_type)?;
                        bind.add_result_column(field_name, logical_type);
                    }
                }
            },
            _ => {
                // Single primitive - single column
                let logical_type = create_duckdb_type(&schema.root_type)?;
                bind.add_result_column("column0", logical_type);
            }
        }

        Ok(Self::BindData {
            file_path,
            schema,
        })
    }

    fn init(_: &InitInfo) -> duckdb::Result<Self::InitData, Box<dyn std::error::Error>> {
        Ok(Self::InitData {
            finished: std::sync::atomic::AtomicBool::new(false),
        })
    }

    fn func(
        info: &duckdb::vtab::TableFunctionInfo<Self>,
        output: &mut DataChunkHandle,
    ) -> duckdb::Result<(), Box<dyn std::error::Error>> {
        use std::sync::atomic::Ordering;

        let init = info.get_init_data();
        let bind = info.get_bind_data();

        // Check if we've already processed the file
        if init.finished.load(Ordering::Relaxed) {
            output.set_len(0);
            return Ok(());
        }

        // Process the JSON file (second pass)
        let processor = crate::schema::TwoPassJsonProcessor::new(&bind.file_path, SchemaInferenceConfig {
            enable_debug_output: false,
        })?;

        let rows_processed = processor.process_to_vectors(output)?;

        eprintln!("Processed {} rows from {}", rows_processed, bind.file_path);

        // Mark as finished
        init.finished.store(true, Ordering::Relaxed);

        Ok(())
    }

    fn parameters() -> Option<Vec<duckdb::core::LogicalTypeHandle>> {
        Some(vec![duckdb::core::LogicalTypeHandle::from(LogicalTypeId::Varchar)])
    }
}

/// Create DuckDB logical type from inferred JSON type
pub fn create_duckdb_type(json_type: &InferredJsonType) -> Result<duckdb::core::LogicalTypeHandle, Box<dyn std::error::Error>> {
    match json_type {
        InferredJsonType::Null => Ok(duckdb::core::LogicalTypeHandle::from(LogicalTypeId::Varchar)),
        InferredJsonType::Boolean => Ok(duckdb::core::LogicalTypeHandle::from(LogicalTypeId::Boolean)),
        InferredJsonType::Number => Ok(duckdb::core::LogicalTypeHandle::from(LogicalTypeId::Double)),
        InferredJsonType::String => Ok(duckdb::core::LogicalTypeHandle::from(LogicalTypeId::Varchar)),
        InferredJsonType::Array { element_type, .. } => {
            let child_type = create_duckdb_type(element_type)?;
            Ok(duckdb::core::LogicalTypeHandle::list(&child_type))
        },
        InferredJsonType::Object { fields, .. } => {
            // Create proper STRUCT type with field types
            let mut struct_fields = Vec::new();
            for (field_name, field_type) in fields {
                let field_logical_type = create_duckdb_type(field_type)?;
                struct_fields.push((field_name.as_str(), field_logical_type));
            }
            Ok(duckdb::core::LogicalTypeHandle::struct_type(&struct_fields))
        },
    }
}

#[duckdb_entrypoint_c_api()]
pub unsafe fn streaming_json_reader_init(db: duckdb::Connection) -> duckdb::Result<(), Box<dyn std::error::Error>> {
    db.register_table_function::<JsonReaderVTab>("streaming_json_reader")
        .map_err(|e| format!("Failed to register table function: {}", e))?;

    eprintln!("Extension loaded successfully - table function registered");
    Ok(())
}
