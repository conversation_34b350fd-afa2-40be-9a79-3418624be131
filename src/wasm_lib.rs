// To build the Wasm target, a `staticlib` crate-type is required
//
// This is different than the default needed in native, and there is
// currently no way to select crate-type depending on target.
//
// This file sole purpose is remapping the content of lib as an
// example, do not change the content of the file.
//
// To build the Wasm target explicitly, use:
//   cargo build --example $PACKAGE_NAME

// Include all the modules directly
pub mod types;
pub mod paths;
pub mod utils;
pub mod vectors;
pub mod processing;
pub mod schema;
pub mod vtab;

// Re-export main public API
pub use schema::{TwoPassJsonProcessor, InferredSchema, infer_schema_with_capacities, infer_schema_from_file};
pub use types::{InferredJsonType, TempJsonValue, SchemaInferenceConfig};
pub use paths::{VectorPath, ProjectionPath, VectorCapacities};
pub use vtab::{JsonReaderVTab, streaming_json_reader_init};
